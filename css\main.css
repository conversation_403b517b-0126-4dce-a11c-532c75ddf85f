/* Variables */
:root {
    --primary-color: #0056b3;
    --accent-color: #00a0e9;
    --text-color: #333;
    --light-gray: #f5f5f5;
    --transition: all 0.3s ease;
    --spacing-sm: 1rem;
    --spacing-md: 2rem;
    --spacing-lg: 3rem;
    --container-padding: 5%;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
}

.wrapper {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--container-padding);
}

/* Header and Navigation */
header {
    background: #fff;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
}

.main-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 5%;
    max-width: 1400px;
    margin: 0 auto;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

@media (max-width: 768px) {
    .nav-container {
        padding: 0 0.8rem;
    }
}

.logo {
    font-size: 1.8rem;
    font-weight: bold;
    color: #2c3e50;
}

.logo img {
    height: 40px;
    width: auto;
    max-width: 150px;
    object-fit: contain;
}

@media (max-width: 768px) {
    .logo img {
        height: 35px;
        max-width: 120px;
    }
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2rem;
    align-items: center;
}

.nav-links a {
    text-decoration: none;
    color: #2c3e50;
    font-weight: 500;
    transition: color 0.3s;
}

.nav-links a:hover {
    color: #3498db;
}

.nav-links .active a {
    color: #3498db;
}

.contact-btn {
    background: #3498db;
    color: white !important;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.contact-btn:hover {
    background: #2980b9;
}

.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #2c3e50;
}

/* Main Content */
main {
    margin-top: 70px; /* Reduced to match header height */
    flex: 1;
}

/* Carousel Section */
.carousel-container {
    position: relative;
    overflow: hidden;
    height: 100vh;
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    margin-right: calc(-50vw + 50%);
    margin-top: -70px; /* Offset the header height */
}

.carousel {
    position: relative;
    height: 100%;
    width: 100%;
    overflow: hidden;
}

.carousel-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 1s ease-in-out;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #000;
}

.carousel-slide.active {
    opacity: 1;
    z-index: 1;
}

.carousel-slide.smooth-transition {
    transition: opacity 1.5s ease-in-out;
}

.carousel-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0.8; /* Slightly darken the image for better text visibility */
}

.carousel-caption {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
    padding: var(--spacing-md);
    z-index: 2;
    width: 80%;
    max-width: 1000px;
}

.carousel-caption h1 {
    font-size: 4.5rem;
    margin-bottom: var(--spacing-md);
    text-transform: uppercase;
    letter-spacing: 3px;
    font-weight: 800;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 1s forwards 0.5s;
}

.carousel-caption h2 {
    font-size: 2.5rem;
    font-weight: 300;
    letter-spacing: 1px;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 1s forwards 0.8s;
}

.highlight {
    color: var(--accent-color);
    position: relative;
    display: inline-block;
}

.highlight::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--accent-color);
    transform: scaleX(0);
    transform-origin: left;
    animation: scaleIn 1s forwards 1.2s;
}

@keyframes scaleIn {
    to {
        transform: scaleX(1);
    }
}

.carousel-nav {
    position: absolute;
    bottom: 50px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-md);
    z-index: 10;
}

.carousel-prev,
.carousel-next {
    background: rgba(0, 0, 0, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    color: white;
    font-size: 1.2rem;
    opacity: 0;
    animation: fadeIn 0.5s forwards 1.5s;
}

.carousel-prev:hover,
.carousel-next:hover {
    background: var(--accent-color);
    border-color: white;
    transform: scale(1.1);
}

.carousel-dots {
    display: flex;
    gap: 15px;
}

.dot {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
    opacity: 0;
    animation: fadeIn 0.5s forwards 1.5s;
}

.dot.active {
    background: var(--accent-color);
    border-color: white;
    transform: scale(1.2);
}

@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

/* Footer */
footer {
    background: #2c3e50;
    color: white;
    padding: 4rem 5% 2rem;
}

.footer-content {
    max-width: 1400px;
    margin: 0 auto;
}

.footer-nav {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.footer-col h3 {
    margin-bottom: 1rem;
}

.footer-col ul {
    list-style: none;
}

.footer-col a {
    color: white;
    text-decoration: none;
    opacity: 0.8;
    transition: opacity 0.3s;
}

.footer-col a:hover {
    opacity: 1;
}

/* Map styling in footer */
.footer-col iframe {
    width: 100%;
    max-width: 300px;
    height: 200px;
    border-radius: 8px;
    margin-top: 0.5rem;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    display: block;
}

.copyright {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255,255,255,0.1);
}

.social-icons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1rem;
}

.social-icon {
    color: white;
    font-size: 1.2rem;
    opacity: 0.8;
    transition: opacity 0.3s;
}

.social-icon:hover {
    opacity: 1;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background: #3498db;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    opacity: 0;
    transition: opacity 0.3s;
}

.back-to-top.visible {
    opacity: 1;
}

/* Page-specific Styles */

/* Home Page */
.intro {
    padding: 5rem 5%;
    display: flex;
    gap: 4rem;
    max-width: 1400px;
    margin: 0 auto;
}

.intro-text {
    flex: 1;
}

.intro-text h2 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    color: #2c3e50;
}

.intro-description {
    margin-bottom: 2rem;
}

.intro-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--accent-color);
}

.intro-image {
    flex: 1;
    position: relative;
}

.image-container {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.image-container img {
    width: 100%;
    height: auto;
    display: block;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(0,0,0,0), rgba(0,0,0,0.3));
}

.floating-badge {
    position: absolute;
    bottom: -20px;
    right: 20px;
    background: white;
    border-radius: 50%;
    width: 80px;
    height: 80px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.badge-icon {
    font-size: 2rem;
    color: var(--accent-color);
    margin-bottom: 0.5rem;
}

.badge-text {
    font-size: 0.8rem;
    font-weight: bold;
}

.why-us {
    background: var(--light-gray);
    padding: 5rem 5%;
}

.why-us-container {
    max-width: 1400px;
    margin: 0 auto;
}

.why-us-header {
    text-align: center;
    margin-bottom: 4rem;
}

.features {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.feature {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.feature:hover {
    transform: translateY(-5px);
}

.feature-icon-wrapper {
    font-size: 2rem;
    color: var(--accent-color);
    margin-bottom: 1rem;
}

.products {
    padding: 5rem 5%;
    background-color: #f9fbfd;
}

.products-container {
    max-width: 1400px;
    margin: 0 auto;
}

.products-header {
    text-align: center;
    margin-bottom: 4rem;
}

.products-header h2 {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 1rem;
    position: relative;
    display: inline-block;
}

.products-header h2:after {
    content: '';
    position: absolute;
    width: 60px;
    height: 3px;
    background-color: var(--accent-color);
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
}

.products-subtitle {
    font-size: 1.2rem;
    color: #666;
    max-width: 700px;
    margin: 1.5rem auto 0;
}

/* Product Gallery Styles */
.products-gallery {
    padding: 4rem 0;
    background-color: #f9fbfd;
}
.product-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}
.product-card {
    background: #fff !important;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}
.product-card:hover {
    transform: translateY(-5px);
}
.product-image {
    height: 200px;
    overflow: hidden;
}
.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
.product-content {
    padding: 1.5rem;
}
.product-content h3 {
    color: #0056b3;
    margin-bottom: 0.5rem;
}
.product-specs {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
}
.product-specs span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #666;
}
@media (max-width: 768px) {
    .product-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}
@media (max-width: 480px) {
    .product-grid {
        grid-template-columns: 1fr;
        max-width: 300px;
        margin: 1rem auto;
    }
}

.product-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2.5rem;
    margin-bottom: 4rem;
    align-items: stretch;
}

.product-item {
    display: flex;
    height: 100%;
    align-items: stretch;
}

.product-card {
    text-decoration: none;
    color: inherit;
    text-align: center;
    padding: 0;
    background: white;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    width: 100%;
    overflow: hidden;
    height: 100%;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.product-image {
    background-color: #f5f9ff;
    border-radius: 12px 12px 0 0;
    position: relative;
    overflow: hidden;
    height: 200px;
}

.product-image:before {
    content: '';
    position: absolute;
    width: 150%;
    height: 100px;
    background-color: rgba(255, 255, 255, 0.1);
    top: -50px;
    left: -25%;
    transform: rotate(-5deg);
    z-index: 1;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.1);
}

/* Keep for backward compatibility */
.product-placeholder {
    font-size: 3.5rem;
    color: var(--accent-color);
    margin-bottom: 0.5rem;
    transition: transform 0.3s ease;
}

.product-card:hover .product-placeholder {
    transform: scale(1.1);
}

.product-card h3 {
    padding: 1.5rem;
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    background-color: white;
    border-radius: 0 0 12px 12px;
}

.products-cta {
    text-align: center;
    margin-top: 2rem;
}

.btn-view-all {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.8rem;
    text-decoration: none;
    color: white;
    font-weight: 600;
    background-color: var(--accent-color);
    padding: 1rem 2rem;
    border-radius: 50px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 160, 233, 0.2);
    position: relative;
    overflow: hidden;
}

.btn-view-all span {
    position: relative;
    z-index: 2;
}

.btn-view-all i {
    position: relative;
    z-index: 2;
    transition: transform 0.3s ease;
}

.btn-view-all:hover {
    background-color: var(--primary-color);
    box-shadow: 0 6px 20px rgba(0, 86, 179, 0.3);
    transform: translateY(-3px);
}

.btn-view-all:hover i {
    transform: translateX(4px);
}

.btn-secondary {
    display: inline-block;
    background: transparent;
    color: var(--accent-color);
    border: 2px solid var(--accent-color);
    padding: 0.8rem 2rem;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s;
    margin-left: 1rem;
}

.btn-secondary:hover {
    background: var(--accent-color);
    color: white;
}

.plant-parallax {
    background: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), url('https://images.unsplash.com/photo-1563453392212-326f5e854473?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80');
    background-attachment: fixed;
    background-size: cover;
    background-position: center;
    color: white;
    padding: 5rem 5%;
}

.plant-content {
    max-width: 1400px;
    margin: 0 auto;
    text-align: center;
}

.plant-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin: 4rem 0;
}

.plant-feature {
    text-align: center;
}

.feature-icon {
    font-size: 2.5rem;
    color: white;
    margin-bottom: 1rem;
}

.btn-learn-more {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: white;
    color: var(--text-color);
    padding: 0.8rem 2rem;
    border-radius: 4px;
    text-decoration: none;
    transition: transform 0.3s;
}

.btn-learn-more:hover {
    transform: translateY(-3px);
}

/* About Page */
.about-hero, .products-hero, .plant-hero, .contact-hero {
    height: 400px;
    background: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), url('../images/hero-bg.jpg');
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
    margin-bottom: 3rem;
}

.about-hero-content, .products-hero-content, .plant-hero-content, .contact-hero-content {
    max-width: 800px;
    padding: 0 2rem;
}

.about-hero-content h1, .products-hero-content h1, .plant-hero-content h1, .contact-hero-content h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.company-details, .products-intro, .plant-overview, .contact-info-section {
    padding: 4rem 0;
}

.company-info, .plant-row {
    display: flex;
    gap: 3rem;
    margin-bottom: 3rem;
    align-items: flex-start;
}

.company-text, .plant-content {
    flex: 1;
    width: 100%;
}

.company-image, .plant-image {
    flex: 1;
    width: 100%;
}

.plant-image img {
    width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.image-caption {
    text-align: center;
    font-style: italic;
    color: #666;
    margin-top: 1rem;
    font-size: 0.9rem;
}

.plant-stats {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin: 2rem 0;
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.plant-stats .stat-item {
    text-align: center;
    flex: 1;
    min-width: 120px;
    padding: 1rem;
}

.plant-stats .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: 0.5rem;
}

.plant-stats .stat-label {
    font-size: 1rem;
    color: #555;
    font-weight: 600;
}

.plant-features-list {
    list-style: none;
    margin: 2rem 0;
}

.plant-features-list li {
    display: flex;
    align-items: flex-start;
    margin-bottom: 1.5rem;
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    transition: transform 0.3s ease;
}

.plant-features-list li:hover {
    transform: translateX(5px);
}

.plant-features-list i {
    color: var(--accent-color);
    font-size: 1.2rem;
    margin-right: 1rem;
    margin-top: 0.2rem;
}

.feature-content {
    flex: 1;
}

.feature-content strong {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.feature-content p {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.5;
}

/* Responsive adjustments for plant.html */
@media (max-width: 768px) {
  .about-hero, .products-hero, .plant-hero, .contact-hero {
    height: 300px;
    background-position: center;
  }

  .plant-hero-content h1 {
    font-size: 1.5rem;
  }

  .plant-hero-content p {
    font-size: 1rem;
  }

  .plant-row {
    flex-direction: column;
  }

  .plant-image img {
    width: 100%;
    height: auto;
  }

  .plant-stats {
    flex-wrap: wrap;
  }

  .stat-item {
    width: 50%;
    margin-bottom: 1rem;
  }
}

@media (max-width: 480px) {
  .plant-hero-content h1 {
    font-size: 1.2rem;
  }

  .plant-hero-content p {
    font-size: 0.9rem;
  }

  .stat-item {
    width: 100%;
  }

  .plant-features-list li {
    flex-direction: column;
    align-items: flex-start;
  }

  .plant-features-list li i {
    margin-bottom: 0.5rem;
  }
}

@media (max-width: 360px) {
  .plant-hero-content h1 {
    font-size: 1rem;
  }

  .plant-hero-content p {
    font-size: 0.8rem;
  }

  .cta-buttons a {
    font-size: 0.8rem;
    padding: 0.5rem 1rem;
  }
}

@media (max-width: 320px) {
  .plant-hero {
    padding: 1rem;
  }

  .plant-hero-content h1 {
    font-size: 0.9rem;
  }

  .plant-hero-content p {
    font-size: 0.7rem;
  }

  .cta-buttons a {
    font-size: 0.7rem;
    padding: 0.4rem 0.8rem;
  }

  .main-nav {
    padding: 0.8rem;
  }

  .mobile-menu-btn {
    padding: 0.5rem;
    font-size: 1.2rem;
  }

  .logo {
    font-size: 1.2rem;
    padding: 0.5rem;
  }

  .nav-links {
    top: 100%;
    padding: 0.8rem;
  }

  .nav-links li {
    padding: 0.8rem 0;
  }

  .nav-links a {
    font-size: 1rem;
    padding: 0.5rem;
  }

  .contact-btn {
    padding: 0.5rem 1rem !important;
    margin-top: 0.5rem;
  }
}

/* Company Timeline */
.company-timeline {
    margin: 3rem 0;
    position: relative;
}

.company-timeline::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 20px;
    width: 2px;
    background-color: var(--accent-color);
}

.timeline-item {
    position: relative;
    padding-left: 60px;
    margin-bottom: 2rem;
}

.timeline-year {
    position: absolute;
    left: 0;
    top: 5px; /* Adjusted to align with content */
    width: 40px;
    height: 40px;
    background-color: var(--accent-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1rem;
    z-index: 1;
    text-align: center;
    line-height: 1;
    padding: 0;
    box-shadow: 0 4px 10px rgba(0,160,233,0.3);
}

.timeline-content {
    background-color: #f9f9f9;
    padding: 1.2rem;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.timeline-content h4 {
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

/* Company Achievements */
.company-achievements {
    display: flex;
    justify-content: space-around;
    margin-top: 2rem;
    background-color: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.achievement-item {
    text-align: center;
    padding: 0 1rem;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.achievement-icon {
    font-size: 2rem;
    color: var(--accent-color);
    margin-bottom: 1rem;
}

.achievement-number {
    width: 100px;
    height: 100px;
    background-color: var(--accent-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
    box-shadow: 0 4px 10px rgba(0,160,233,0.3);
    text-align: center;
    line-height: 1;
}

.achievement-text {
    font-size: 1rem;
    color: #555;
    font-weight: 600;
    margin-top: 0.5rem;
}

.values-heading {
    margin: 2.5rem 0 1.5rem;
    text-align: center;
}

.company-values, .tech-highlights {
    display: flex;
    flex-wrap: wrap;
    gap: 2rem;
    margin-top: 2rem;
}

.value-item, .tech-item {
    text-align: center;
    flex: 1;
    min-width: 200px;
    background-color: #f9f9f9;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.value-item:hover, .tech-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.value-icon, .tech-icon {
    font-size: 2.5rem;
    color: var(--accent-color);
    margin-bottom: 1rem;
    transition: transform 0.3s ease;
}

/* Tech Item Specific Styles */
.tech-item {
    text-align: left;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.tech-icon {
    width: 60px;
    height: 60px;
    background: rgba(0, 160, 233, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.tech-info h3 {
    margin-bottom: 1rem;
    color: var(--primary-color);
    font-size: 1.3rem;
}

.tech-info p {
    color: #666;
    line-height: 1.6;
}

.tech-certifications {
    margin-top: 3rem;
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.tech-certifications h3 {
    margin-bottom: 1.5rem;
    color: var(--primary-color);
    text-align: center;
}

.cert-badges {
    display: flex;
    justify-content: space-around;
    flex-wrap: wrap;
    gap: 1.5rem;
}

.cert-badge {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 1rem;
    min-width: 150px;
}

.cert-badge i {
    font-size: 2rem;
    color: var(--accent-color);
    margin-bottom: 0.8rem;
}

.cert-badge span {
    font-weight: 600;
    color: #555;
}

.value-item:hover .value-icon {
    transform: scale(1.1);
}

.value-item h3 {
    margin-bottom: 0.8rem;
    color: var(--primary-color);
}

.our-team, .product-categories, .plant-technology, .plant-compliance {
    padding: 4rem 0;
}

/* Our Values Section */
.our-values {
    padding: 5rem 0;
    background-color: #f9fbfd;
}

.our-values .company-values {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.section-header {

    text-align: center;
    margin: 3rem 0;
}

.section-subtitle {
    font-size: 1.2rem;
    color: #666;
    max-width: 700px;
    margin: 0 auto;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.team-member {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.team-member:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.15);
}

.member-image {
    position: relative;
    overflow: hidden;
}

.member-image img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.team-member:hover .member-image img {
    transform: scale(1.05);
}

.member-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to top, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 60%);
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    padding-bottom: 1.5rem;
}

.team-member:hover .member-overlay {
    opacity: 1;
}

.member-info {
    padding: 1.5rem;
}

.member-position {
    color: var(--accent-color);
    margin-bottom: 1rem;
    font-weight: 600;
}

.member-bio {
    color: #666;
    line-height: 1.6;
}

.member-social {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.member-social a {
    color: white;
    background: rgba(255,255,255,0.2);
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.member-social a:hover {
    background: var(--accent-color);
    transform: translateY(-3px);
}

.about-image-section, .products-cta, .plant-cta {
    padding: 0;
    color: white;
    text-align: center;
}

.parallax-image {
    height: 70vh; /* Responsive height based on viewport */
    min-height: 400px; /* Minimum height for very small screens */
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    position: relative;
}

.overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.6);
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-content {
    max-width: 900px;
    width: 90%; /* Add width constraint for smaller screens */
    padding: 2rem;
    z-index: 2;
}

.content-animation {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 1s forwards 0.3s;
}

.subtitle {
    display: inline-block;
    font-size: 1.2rem;
    font-weight: 300;
    letter-spacing: 2px;
    margin-bottom: 1.5rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(255,255,255,0.3);
}

.image-content h2 {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.image-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.cta-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Compliance Section Styles */
.compliance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin: 2.5rem 0;
}

.compliance-item {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    text-align: center;
    transition: transform 0.3s ease;
}

.compliance-item:hover {
    transform: translateY(-10px);
}

.compliance-icon {
    width: 70px;
    height: 70px;
    background: rgba(0, 160, 233, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.compliance-icon i {
    font-size: 2rem;
    color: var(--accent-color);
}

.compliance-item h3 {
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.compliance-item p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.compliance-year {
    display: inline-block;
    background: var(--light-gray);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--primary-color);
}

.quality-commitment {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    margin-top: 3rem;
}

.quality-commitment h3 {
    margin-bottom: 1.5rem;
    color: var(--primary-color);
}

.quality-points {
    list-style: none;
    margin-top: 1.5rem;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.quality-points li {
    display: flex;
    align-items: center;
}

.quality-points i {
    color: var(--accent-color);
    margin-right: 0.8rem;
}

/* Tour Features Styles */
.tour-features {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin: 2rem 0;
}

.tour-feature {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem 1.5rem;
    border-radius: 8px;
    backdrop-filter: blur(5px);
}

.tour-feature i {
    font-size: 1.8rem;
    color: var(--accent-color);
    margin-bottom: 0.8rem;
}

.tour-feature span {
    font-weight: 600;
    font-size: 0.9rem;
}

.testimonial-quote {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: 8px;
    margin: 2rem 0;
    position: relative;
    backdrop-filter: blur(5px);
}

.testimonial-quote i {
    position: absolute;
    top: 1rem;
    left: 1rem;
    font-size: 2rem;
    color: rgba(255, 255, 255, 0.2);
}

.testimonial-quote blockquote {
    font-style: italic;
    margin-bottom: 1rem;
    font-size: 1.1rem;
    line-height: 1.6;
}

.testimonial-quote cite {
    display: block;
    text-align: right;
    font-style: normal;
    font-weight: 600;
}

.btn-primary {
    display: inline-block;
    background: var(--accent-color);
    color: white;
    padding: 1rem 2.5rem;
    border-radius: 50px;
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 600;
    letter-spacing: 1px;
    box-shadow: 0 4px 15px rgba(0,160,233,0.3);
}

.btn-primary:hover {
    background: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0,86,179,0.4);
}

.btn-secondary {
    display: inline-block;
    background: transparent;
    color: white;
    padding: 0.95rem 2.5rem;
    border-radius: 50px;
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 600;
    letter-spacing: 1px;
    border: 2px solid white;
}

.btn-secondary:hover {
    background: white;
    color: var(--primary-color);
    transform: translateY(-3px);
}

.highlight-text {
    color: var(--accent-color);
}

/* Trust Metrics Section */
.trust-metrics {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    gap: 2rem;
    margin: 3rem 0;
}

.metric-item {
    text-align: center;
    flex: 1;
    min-width: 200px;
    max-width: 250px;
    padding: 2rem 1rem;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    transition: transform 0.3s ease;
}

.metric-item:hover {
    transform: translateY(-10px);
}

.metric-icon {
    font-size: 2.5rem;
    color: var(--accent-color);
    margin-bottom: 1rem;
}

.metric-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.metric-label {
    color: #666;
    font-size: 1.1rem;
}



/* Contact Page */
/* Hero Section Styles */
.contact-hero {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
}

.hero-animation {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 1s forwards 0.3s;
}

.contact-hero-content {
    max-width: 800px;
    padding: 0 2rem;
    z-index: 2;
}

.contact-hero h1 {
    font-size: 3.5rem;
    margin-bottom: 1.5rem;
    letter-spacing: 1px;
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

.hero-cta {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    margin-top: 2rem;
}

.hero-badges {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin-top: 2rem;
}

.hero-badge {
    display: flex;
    flex-direction: column;
    align-items: center;
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem 1.5rem;
    border-radius: 8px;
    backdrop-filter: blur(5px);
}

.hero-badge i {
    font-size: 1.8rem;
    color: var(--accent-color);
    margin-bottom: 0.8rem;
}

.hero-badge span {
    font-weight: 600;
    font-size: 0.9rem;
}



/* FAQ Styles */
.faq-section {
    padding: 5rem 0;
    background-color: #f9fbfd;
}

.faq-header {
    text-align: center;
    margin-bottom: 3rem;
}

.faq-header h2 {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 1rem;
    position: relative;
    display: inline-block;
}

.faq-header h2:after {
    content: '';
    position: absolute;
    width: 60px;
    height: 3px;
    background-color: var(--accent-color);
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
}

.faq-wrapper {
    max-width: 900px;
    margin: 0 auto;
}

.faq-container {
    margin-bottom: 3rem;
}

.faq-item {
    background: white;
    border-radius: 8px;
    margin-bottom: 1.5rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.faq-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.faq-question {
    padding: 1.5rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.question-icon {
    margin-right: 1rem;
    font-size: 1.2rem;
    color: var(--accent-color);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(0, 160, 233, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.faq-question h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
    flex: 1;
}

.faq-toggle {
    color: var(--accent-color);
    font-size: 1.2rem;
    transition: transform 0.3s ease;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.faq-answer {
    padding: 0 1.5rem 0 4.5rem;
    max-height: 0;
    overflow: hidden;
    transition: all 0.5s ease;
    opacity: 0;
}

.faq-item.active .faq-answer {
    padding: 0 1.5rem 1.5rem 4.5rem;
    max-height: 1000px;
    opacity: 1;
}

.faq-item.active .faq-toggle {
    transform: rotate(45deg);
}

.faq-answer p {
    margin-bottom: 1rem;
    line-height: 1.6;
    color: #555;
}



/* Contact Page - Main Styles */
.contact-columns {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.contact-column {
    background: white;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 5px 20px rgba(0,0,0,0.05);
    transition: transform 0.3s ease;
}

.contact-column:hover {
    transform: translateY(-5px);
}

.column-header {
    margin-bottom: 2rem;
    text-align: center;
}

.column-header h3 {
    font-size: 1.8rem;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.location-card, .contact-card {
    display: flex;
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid var(--light-gray);
    transition: transform 0.3s ease;
}

.location-card:hover, .contact-card:hover {
    transform: translateX(5px);
}

.location-card:last-child, .contact-card:last-child {
    border-bottom: none;
    padding-bottom: 0;
    margin-bottom: 0;
}

.location-icon, .contact-card-icon {
    font-size: 1.8rem;
    color: var(--accent-color);
    margin-right: 1.5rem;
    width: 50px;
    height: 50px;
    background: rgba(0, 160, 233, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.location-info h4, .contact-card-info h4 {
    margin-bottom: 0.8rem;
    color: #2c3e50;
    font-size: 1.2rem;
}

.location-info p, .contact-card-info p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 0.5rem;
}

.location-detail {
    margin-top: 0.8rem;
    display: flex;
    align-items: center;
}

.location-detail i {
    margin-right: 0.8rem;
    color: var(--accent-color);
    font-size: 0.9rem;
}

.contact-card-info a {
    color: var(--accent-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

.contact-card-info a:hover {
    color: var(--primary-color);
    text-decoration: underline;
}

.text-muted {
    color: #999;
    font-size: 0.9rem;
    font-style: italic;
}

.social-media-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 1rem;
    margin-top: 1.5rem;
}

.social-media-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: #555;
    padding: 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    background: #f9f9f9;
}

.social-media-item i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--accent-color);
}

.social-media-item span {
    font-size: 0.9rem;
    font-weight: 600;
}

.social-media-item:hover {
    background: var(--accent-color);
    color: white;
    transform: translateY(-5px);
}

.social-media-item:hover i {
    color: white;
}

.map-section {
    padding: 4rem 0;
}

.map-header {
    text-align: center;
    margin-bottom: 2rem;
}

.map-header h2 {
    font-size: 2.2rem;
    color: #2c3e50;
    margin-bottom: 0.8rem;
}

.map-container {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 25px rgba(0,0,0,0.1);
    position: relative;
}

.map-container iframe {
    display: block;
}

/* Animation Styles */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.feature, .product-item {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.feature.animated, .product-item.animated {
    opacity: 1;
    transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 768px) {
    .mobile-menu-btn {
        display: block;
    }

    .footer-nav {
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-col iframe {
        max-width: 100%;
        height: 180px;
    }

    .nav-links {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background: white;
        flex-direction: column;
        padding: 1rem;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        z-index: 1000;
    }

    .nav-links.active,
    .nav-links.show {
        display: flex;
    }

    /* Home page responsive */
    .intro {
        flex-direction: column;
    }

    .carousel-container {
        height: 80vh;
    }

    .carousel-caption {
        width: 90%;
    }

    .carousel-caption h1 {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }

    .carousel-caption h2 {
        font-size: 1.5rem;
    }

    .carousel-nav {
        bottom: 30px;
    }

    .carousel-prev,
    .carousel-next {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .dot {
        width: 12px;
        height: 12px;
    }

    .intro-stats {
        flex-direction: column;
    }

    .stat-item {
        margin-bottom: 1rem;
    }

    .plant-features {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    /* Other pages responsive */
    .company-info, .plant-row {
        flex-direction: column;
        gap: 2rem;
    }

    .company-text, .company-image {
        width: 100%;
    }

    .company-image {
        order: -1; /* Move image to the top on mobile */
    }

    .company-values, .tech-highlights {
        flex-direction: column;
        align-items: center;
    }

    .about-hero-content h1, .products-hero-content h1, .plant-hero-content h1, .contact-hero-content h1 {
        font-size: 2rem;
    }

    /* About image section responsive */
    .parallax-image {
        background-attachment: scroll; /* Disable fixed attachment on mobile for better performance */
        height: 60vh;
    }

    .image-content h2 {
        font-size: 2.2rem;
    }

    .image-content p {
        font-size: 1rem;
    }

    .subtitle {
        font-size: 1rem;
    }

    /* Product section responsive styles */
    .product-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
        padding: 0 1rem;
        margin: 2rem 0;
    }

    .features {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
        padding: 0 1rem;
        margin: 2rem 0;
    }

    /* About page responsive */
    .flip-card {
        height: 300px;
    }

    .modal-header {
        flex-direction: column;
        align-items: center;
        text-align: center;
    }

    .modal-header img {
        margin-right: 0;
        margin-bottom: 20px;
    }

    .expertise-list {
        grid-template-columns: 1fr;
    }

    /* Additional about page responsive styles */
    .company-timeline {
        margin: 2rem 0;
    }

    .timeline-item {
        padding-left: 50px;
    }

    .company-achievements {
        flex-wrap: wrap;
        justify-content: center;
        gap: 1rem;
    }

    .achievement-item {
        flex-basis: 45%;
        margin-bottom: 1rem;
    }

    .value-item {
        min-width: 100%;
        margin-bottom: 1rem;
    }
}

@media (max-width: 480px) {
    .carousel-container {
        height: 70vh;
    }

    .footer-nav {
        grid-template-columns: 1fr;
    }

    .footer-col iframe {
        max-width: 100%;
        height: 200px;
    }

    .carousel-caption h1 {
        font-size: 1.8rem;
        letter-spacing: 2px;
    }

    .carousel-caption h2 {
        font-size: 1.2rem;
    }

    .carousel-nav {
        bottom: 20px;
        gap: 10px;
    }

    .carousel-prev,
    .carousel-next {
        width: 35px;
        height: 35px;
    }

    .dot {
        width: 10px;
        height: 10px;
    }

    .intro-text h2 {
        font-size: 2rem;
    }

    .feature {
        padding: 1.5rem;
    }

    /* About image section small screen styles */
    .parallax-image {
        height: 50vh; /* Shorter height on very small screens */
        min-height: 350px;
    }

    .image-content h2 {
        font-size: 1.8rem;
    }

    .image-content p {
        font-size: 0.9rem;
        line-height: 1.5;
    }

    .subtitle {
        font-size: 0.9rem;
        letter-spacing: 1px;
    }

    .cta-buttons {
        flex-direction: column;
        gap: 1rem;
    }

    .btn-primary, .btn-secondary {
        width: 100%;
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }

    /* Contact page social media icons for mobile */
    .social-media-grid {
        grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
        gap: 0.5rem;
    }

    .social-media-item {
        padding: 0.7rem;
    }

    .social-media-item i {
        font-size: 1.1rem;
        margin-bottom: 0.3rem;
    }

    .social-media-item span {
        font-size: 0.7rem;
    }

    /* Client section small screen styles */
    .clients {
        padding: 3rem 5%;
    }

    .clients h2 {
        font-size: 2rem;
    }

    .client-logo {
        padding: 0.5rem 1rem;
        min-width: 140px;
        margin: 0 1rem;
    }

    .client-logo img {
        height: 30px;
    }

    .marquee-content {
        animation: marquee 20s linear infinite;
    }

    /* Product section small screen styles */
    .products {
        padding: 4rem 5% 3rem;
    }

    .products-header h2 {
        font-size: 2rem;
    }

    .products-subtitle {
        font-size: 1rem;
    }

    .product-grid {
        grid-template-columns: 1fr;
        max-width: 300px;
        margin-left: auto;
        margin-right: auto;
    }

    .product-image {
        padding: 2rem 1rem;
    }

    .product-card h3 {
        padding: 1.2rem;
        font-size: 1.1rem;
    }

    .btn-view-all {
        padding: 0.8rem 1.8rem;
        font-size: 0.9rem;
    }

    /* Additional about page small screen styles */
    .company-achievements {
        padding: 1rem;
    }

    .achievement-item {
        flex-basis: 100%;
    }

    .timeline-item {
        padding-left: 40px;
    }

    .timeline-year {
        width: 30px;
        height: 30px;
        font-size: 0.8rem;
        line-height: 1;
        padding: 0;
    }

    .company-text h2 {
        font-size: 1.8rem;
        text-align: center;
    }

    .values-heading {
        font-size: 1.5rem;
    }
}

@media (max-width: 360px) {
    /* Extra small screen adjustments for about image section */
    .parallax-image {
        height: 100vh; /* Full height on very small screens for better readability */
        min-height: 300px;
    }

    .image-content {
        padding: 1rem;
    }

    .image-content h2 {
        font-size: 1.5rem;
    }

    .subtitle {
        margin-bottom: 1rem;
    }

    /* Extra small screen adjustments for social media grid */
    .social-media-grid {
        grid-template-columns: repeat(auto-fill, minmax(65px, 1fr));
        gap: 0.3rem;
    }

    .social-media-item {
        padding: 0.5rem;
    }

    .social-media-item i {
        font-size: 1rem;
        margin-bottom: 0.2rem;
    }

    .social-media-item span {
        font-size: 0.6rem;
    }
}

/**
 * EDKEM Pharmaceuticals - About Page Interactive Styles
 * This file contains styles for interactive elements on the About page
 */

/* ===== Interactive Timeline ===== */
.timeline-item {
    position: relative;
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s ease, transform 0.5s ease;
    margin-bottom: 1.5rem;
}

.timeline-item.visible {
    opacity: 1;
    transform: translateY(0);
}

.timeline-item .timeline-content {
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.timeline-item::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 50%;
    width: 2px;
    background-color: #e0e0e0;
    transform: translateX(-50%);
    z-index: -1;
}

.timeline-item:first-child::after {
    top: 50%;
}

.timeline-item:last-child::after {
    bottom: 50%;
}

.timeline-item .timeline-year {
    background-color: var(--accent-color);
    color: white;
    padding: 5px 15px;
    border-radius: 20px;
    display: inline-block;
    margin-bottom: 10px;
    font-weight: bold;
    position: relative;
    z-index: 2;
}

.timeline-item.expanded .timeline-year {
    background-color: var(--primary-color);
}

/* Removed plus/minus icons for simplified timeline */

/* ===== Flip Cards for Core Values ===== */
.flip-card {
    background-color: transparent;
    perspective: 1000px;
    height: 250px;
    cursor: pointer;
}

.flip-card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: transform 0.6s;
    transform-style: preserve-3d;
}

.flip-card.flipped .flip-card-inner {
    transform: rotateY(180deg);
}

.flip-card-front, .flip-card-back {
    position: absolute;
    width: 100%;
    height: 100%;
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.flip-card-front {
    background-color: #f9f9f9;
}

.flip-card-back {
    background-color: var(--primary-color);
    color: white;
    transform: rotateY(180deg);
}

.flip-card-back h3 {
    color: white !important;
    margin-bottom: 15px;
}

.flip-card-back p {
    margin-bottom: 20px;
}

.flip-card-action {
    margin-top: auto;
}

.flip-back-btn {
    background-color: white;
    color: var(--primary-color);
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.flip-back-btn:hover {
    background-color: #f0f0f0;
    transform: scale(1.05);
}

/* ===== Team Member Modals ===== */
.team-member.clickable {
    cursor: pointer;
}

.team-member.clickable::after {
    content: '\f055';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: white;
    color: var(--accent-color);
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
    z-index: 5;
}

.team-member.clickable:hover::after {
    transform: scale(1.1);
    background-color: var(--accent-color);
    color: white;
}

.team-modal-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.7);
    z-index: 1000;
    display: none;
    align-items: center;
    justify-content: center;
}

.team-modal-container.active {
    display: flex;
}

.team-modal {
    background-color: white;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    opacity: 0;
    transform: scale(0.9);
    transition: all 0.3s ease;
}

.team-modal.show {
    opacity: 1;
    transform: scale(1);
}

.team-modal-content {
    position: relative;
    max-height: 90vh;
    overflow-y: auto;
}

.close-modal {
    position: absolute;
    top: 15px;
    right: 15px;
    font-size: 24px;
    color: white;
    cursor: pointer;
    z-index: 10;
    width: 30px;
    height: 30px;
    background-color: rgba(0,0,0,0.3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.close-modal:hover {
    background-color: rgba(0,0,0,0.5);
    transform: scale(1.1);
}

.modal-header {
    display: flex;
    background-color: var(--primary-color);
    color: white;
    padding: 30px;
}

.modal-header img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid white;
    margin-right: 30px;
}

.modal-header-info {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.modal-position {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 15px;
}

.modal-social {
    display: flex;
    gap: 15px;
}

.modal-social a {
    color: white;
    background-color: rgba(255,255,255,0.2);
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.modal-social a:hover {
    background-color: white;
    color: var(--primary-color);
    transform: translateY(-3px);
}

.modal-body {
    padding: 30px;
}

.modal-body h3 {
    color: var(--primary-color);
    margin: 20px 0 10px;
}

.modal-body h3:first-child {
    margin-top: 0;
}

.expertise-list {
    list-style: none;
    margin: 15px 0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.expertise-list li {
    display: flex;
    align-items: center;
}

.expertise-list i {
    color: var(--accent-color);
    margin-right: 10px;
}

/* ===== Enhanced Achievement Counters ===== */
.achievement-item {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.achievement-number {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
}

.progress-circle {
    width: 100px;
    height: 100px;
    margin-bottom: 15px;
}

.circular-chart {
    width: 100%;
    height: 100%;
}

.circle-bg {
    fill: none;
    stroke: #eee;
    stroke-width: 3.8;
}

.circle {
    fill: none;
    stroke: var(--accent-color);
    stroke-width: 3.8;
    stroke-linecap: round;
}

.percentage {
    fill: var(--primary-color);
    font-size: 1.2em;
    text-anchor: middle;
    font-weight: bold;
    dominant-baseline: middle;
}

.counter-number {
    font-size: 2.2rem;
    font-weight: bold;
    color: var(--primary-color);
    display: none; /* Hide the duplicate number */
}



/* ===== FAQ Accordion ===== */
.faq-section {
    padding: 4rem 0;
    background-color: #f9f9f9;
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background-color: white;
    border-radius: 8px;
    margin-bottom: 15px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    overflow: hidden;
    transform: translateY(20px);
    opacity: 0;
    transition: transform 0.5s ease, opacity 0.5s ease, box-shadow 0.3s ease;
}

.faq-item.animated {
    transform: translateY(0);
    opacity: 1;
}

.faq-item.active {
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.faq-question {
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    font-weight: 600;
    color: var(--primary-color);
}

.faq-toggle i {
    transition: all 0.3s ease;
}

/* Make sure these styles don't conflict with the ones above */
.faq-answer {
    max-height: 0;
    overflow: hidden;
    transition: all 0.5s ease;
    opacity: 0;
}

.faq-answer-content {
    padding: 0 20px 20px;
    color: #666;
}

.faq-item.active .faq-answer {
    max-height: 1000px;
    opacity: 1;
    padding: 0 1.5rem 1.5rem 4.5rem;
}

.faq-item.active .faq-toggle i {
    transform: rotate(45deg);
}
