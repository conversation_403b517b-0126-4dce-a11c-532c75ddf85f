/* Optimized CSS for EDEN Pharmaceuticals */

/* Variables */
:root {
    --primary-color: #0056b3;
    --accent-color: #00a0e9;
    --text-color: #333;
    --light-gray: #f5f5f5;
    --transition: all 0.3s ease;
    --spacing-sm: 1rem;
    --spacing-md: 2rem;
    --spacing-lg: 3rem;
    --container-padding: 5%;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
}

.wrapper {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

@media (max-width: 768px) {
    .container {
        padding: 0 1rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 0.5rem;
    }
}

/* Header and Navigation */
header {
    background: #fff;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
}

.logo {
    font-size: 1.8rem;
    font-weight: bold;
    color: #2c3e50;
}

.logo img {
    height: 40px;
    width: auto;
    max-width: 150px;
    object-fit: contain;
}

@media (max-width: 768px) {
    .logo img {
        height: 35px;
        max-width: 120px;
    }
}

.nav-links {
    display: flex;
    list-style: none;
    gap: 2rem;
    align-items: center;
}

.nav-links a {
    text-decoration: none;
    color: #2c3e50;
    font-weight: 500;
    transition: color 0.3s;
}

.nav-links a:hover, .nav-links .active a {
    color: #3498db;
}

/* Dropdown Menu Styles */
.nav-links li {
    position: relative;
}

.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    min-width: 200px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border-radius: 4px;
    padding: 0.5rem 0;
    opacity: 0;
    visibility: hidden;
    transform: translateY(10px);
    transition: all 0.3s ease;
    z-index: 1001;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-menu li {
    margin: 0;
    padding: 0;
    width: 100%;
}

.dropdown-menu a {
    display: block;
    padding: 0.7rem 1.5rem;
    color: #2c3e50;
    font-weight: 400;
    transition: all 0.3s ease;
}

.dropdown-menu a:hover {
    background: #f8f9fa;
    color: var(--accent-color);
    padding-left: 1.8rem;
}

.dropdown > a::after {
    content: '\f107';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-left: 0.5rem;
    transition: transform 0.3s ease;
}

.dropdown:hover > a::after {
    transform: rotate(180deg);
}

.contact-btn {
    background: #3498db;
    color: white !important;
    padding: 0.6rem 1.2rem;
    margin: 0.25rem;
    border-radius: 6px;
    transition: all 0.3s ease;
    min-height: 44px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-weight: 500;
    touch-action: manipulation;
}

.contact-btn:hover {
    background: #2980b9;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

/* Mobile-specific contact button improvements */
@media (max-width: 768px) {
    .contact-btn {
        padding: 0.75rem 1.5rem;
        margin: 0.375rem;
        min-height: 48px;
        font-size: 0.95rem;
        border-radius: 8px;
    }
}

@media (max-width: 480px) {
    .contact-btn {
        padding: 0.875rem 1.75rem;
        margin: 0.5rem;
        min-height: 52px;
        font-size: 1rem;
        width: auto;
        min-width: 120px;
    }
}

.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #2c3e50;
    padding: 0.75rem;
    margin: 0.25rem;
    border-radius: 6px;
    transition: all 0.3s ease;
    min-width: 48px;
    min-height: 48px;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 1002;
    touch-action: manipulation;
}

.mobile-menu-btn:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.mobile-menu-btn:active {
    background-color: rgba(0, 0, 0, 0.1);
    transform: scale(0.95);
}

/* Mobile-specific mobile menu button improvements */
@media (max-width: 768px) {
    .mobile-menu-btn {
        display: flex;
        padding: 0.875rem;
        margin: 0.375rem;
        min-width: 52px;
        min-height: 52px;
        font-size: 1.6rem;
    }
}

@media (max-width: 480px) {
    .mobile-menu-btn {
        padding: 1rem;
        margin: 0.5rem;
        min-width: 56px;
        min-height: 56px;
        font-size: 1.7rem;
    }
}

/* Main Content */
main {
    margin-top: 80px; /* Adjusted to match header height */
    flex: 1;
}

@media (max-width: 768px) {
    main {
        margin-top: 70px;
    }
}

/* Carousel Section */
.carousel-container {
    position: relative;
    overflow: hidden;
    height: 100vh;
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    margin-right: calc(-50vw + 50%);
    margin-top: -70px; /* Offset the header height */
}

.carousel {
    position: relative;
    height: 100%;
    width: 100%;
    overflow: hidden;
}

.carousel-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 1s ease-in-out;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #000;
}

.carousel-slide.active {
    opacity: 1;
    z-index: 1;
}

.carousel-slide.smooth-transition {
    transition: opacity 1.5s ease-in-out;
}

.carousel-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0.8; /* Slightly darken the image for better text visibility */
}

.carousel-caption {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
    padding: var(--spacing-md);
    z-index: 2;
    width: 80%;
    max-width: 1000px;
}

.carousel-caption h1 {
    font-size: 4.5rem;
    margin-bottom: var(--spacing-md);
    text-transform: uppercase;
    letter-spacing: 3px;
    font-weight: 800;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.carousel-caption h2 {
    font-size: 2.5rem;
    font-weight: 300;
    letter-spacing: 1px;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.carousel-nav {
    position: absolute;
    bottom: 50px;
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-md);
    z-index: 10;
}

.carousel-prev,
.carousel-next {
    background: rgba(0, 0, 0, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.7);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    color: white;
    font-size: 1.2rem;
}

.carousel-prev:hover,
.carousel-next:hover {
    background: var(--accent-color);
    border-color: white;
    transform: scale(1.1);
}

.carousel-dots {
    display: flex;
    gap: 15px;
}

.dot {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    border: 2px solid rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot.active {
    background: var(--accent-color);
    border-color: white;
    transform: scale(1.2);
}

/* Hero Sections */
.products-hero, .about-hero, .plant-hero, .contact-hero {
    height: 400px;
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-align: center;
    margin-bottom: 3rem;
}

.products-hero-content {
    max-width: 800px;
    padding: 0 2rem;
}

.products-hero-content h1 {
    font-size: 3rem;
    margin-bottom: 1rem;
}

/* Product Grid - 3x3 Layout for Desktop */
.product-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2.5rem;
    margin-bottom: 4rem;
    align-items: stretch;
}

/* Ensure product grid is responsive by default */
@media (max-width: 1200px) {
    .product-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
    }
}

@media (max-width: 900px) {
    .product-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
        padding: 0 1rem;
        margin: 2rem 0;
    }
}

@media (max-width: 600px) {
    .product-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 0 1.5rem;
        margin: 2rem 0;
    }
}

/* Products CTA Section */
.products-cta {
    background-color: var(--accent-color);
    color: white;
    padding: 4rem 0;
    text-align: center;
    margin-top: 2rem;
}

.cta-content {
    max-width: 800px;
    margin: 0 auto;
}

.cta-content h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.btn-primary {
    display: inline-block;
    background: #fff;
    color: var(--accent-color);
    padding: 1rem 2.5rem;
    border-radius: 50px;
    text-decoration: none;
    transition: all 0.3s ease;
    font-weight: 600;
    letter-spacing: 1px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
}

.product-item {
    display: flex;
    height: 100%;
    align-items: stretch;
}

.product-card {
    text-decoration: none;
    color: inherit;
    text-align: center;
    padding: 0;
    background: white;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    width: 100%;
    overflow: hidden;
    height: 100%;
}

.product-link {
    text-decoration: none;
    color: inherit;
    display: block;
    height: 100%;
    width: 100%;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

/* Touch-friendly product cards for mobile */
@media (max-width: 768px) {
    .product-card {
        transition: all 0.2s ease;
    }

    .product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 20px rgba(0,0,0,0.08);
    }

    .product-card:active {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }
}

.product-image {
    background-color: #f5f9ff;
    border-radius: 12px 12px 0 0;
    position: relative;
    overflow: hidden;
    height: 200px;
}

.product-image:before {
    content: '';
    position: absolute;
    width: 150%;
    height: 100px;
    background-color: rgba(255, 255, 255, 0.1);
    top: -50px;
    left: -25%;
    transform: rotate(-5deg);
    z-index: 1;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.1);
}

.product-card h3 {
    padding: 1.5rem;
    margin: 0;
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    background-color: white;
    border-radius: 0 0 12px 12px;
}

.product-content {
    flex: 1;
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    text-align: left;
}

.product-content h3 {
    color: #0056b3;
    margin-bottom: 0.5rem;
    font-size: 1.2rem;
    padding: 0;
}

.product-content p {
    color: #666;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.product-specs {
    margin-top: auto;
    display: flex;
    justify-content: space-between;
    padding-top: 1rem;
    border-top: 1px solid #eee;
}

.product-specs span {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: #666;
}

/* Home Page Styles */
.intro {
    padding: 5rem 5%;
    display: flex;
    gap: 4rem;
    max-width: 1400px;
    margin: 0 auto;
}

.intro-text {
    flex: 1;
}

.intro-text h2 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    color: #2c3e50;
}

.intro-description {
    margin-bottom: 2rem;
}

.intro-stats {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: var(--accent-color);
}

.intro-image {
    flex: 1;
    position: relative;
}

.image-container {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.image-container img {
    width: 100%;
    height: auto;
    display: block;
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(0,0,0,0), rgba(0,0,0,0.3));
}

.floating-badge {
    position: absolute;
    bottom: -20px;
    right: 20px;
    background: white;
    border-radius: 50%;
    width: 80px;
    height: 80px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.badge-icon {
    font-size: 2rem;
    color: var(--accent-color);
    margin-bottom: 0.5rem;
}

.badge-text {
    font-size: 0.8rem;
    font-weight: bold;
}

.why-us {
    background: var(--light-gray);
    padding: 5rem 5%;
}

.why-us-container {
    max-width: 1400px;
    margin: 0 auto;
}

.why-us-header {
    text-align: center;
    margin-bottom: 4rem;
}

/* Why Choose Us - 3x3 Grid Layout for Desktop */
.features {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
}

.feature {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.feature:hover {
    transform: translateY(-5px);
}

.feature-icon-wrapper {
    font-size: 2rem;
    color: var(--accent-color);
    margin-bottom: 1rem;
}

/* Why Choose Us Responsive Design */
@media (max-width: 1200px) {
    .features {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
    }
}

@media (max-width: 900px) {
    .features {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
        padding: 0 1rem;
        margin: 2rem 0;
    }

    .feature {
        padding: 1.5rem;
        margin-bottom: 1rem;
    }
}

@media (max-width: 600px) {
    .features {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 0 1.5rem;
        margin: 2rem 0;
    }

    .feature {
        padding: 2rem 1.5rem;
        margin-bottom: 1rem;
    }

    .feature-icon-wrapper {
        font-size: 2.5rem;
        margin-bottom: 1.5rem;
    }
}

.btn-primary {
    display: inline-block;
    background: var(--primary-color);
    color: white;
    padding: 0.8rem 2rem;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s;
}

.btn-primary:hover {
    background: var(--accent-color);
    transform: translateY(-3px);
}

.btn-secondary {
    display: inline-block;
    background: transparent;
    color: var(--accent-color);
    border: 2px solid var(--accent-color);
    padding: 0.8rem 2rem;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s;
    margin-left: 1rem;
}

.btn-secondary:hover {
    background: var(--accent-color);
    color: white;
}

/* Section Styling */
.section-header {
    text-align: center;
    margin-bottom: 2rem;
}

.section-header h2 {
    font-size: 2.5rem;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.highlight-text {
    color: var(--accent-color);
}

.section-subtitle {
    font-size: 1.2rem;
    color: #666;
    max-width: 700px;
    margin: 0 auto;
}

/* Footer */
footer {
    background: #2c3e50;
    color: white;
    padding: 4rem 5% 2rem;
}

.footer-content {
    max-width: 1400px;
    margin: 0 auto;
}

.footer-nav {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.footer-col h3 {
    margin-bottom: 1rem;
}

.footer-col ul {
    list-style: none;
}

.footer-col a {
    color: white;
    text-decoration: none;
    opacity: 0.8;
    transition: opacity 0.3s;
}

.footer-col a:hover {
    opacity: 1;
}

/* Map styling in footer */
.footer-col iframe {
    width: 100%;
    max-width: 300px;
    height: 200px;
    border-radius: 8px;
    margin-top: 0.5rem;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    display: block;
}

.copyright {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255,255,255,0.1);
}

.social-icons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 1rem;
}

.social-icon {
    color: white;
    font-size: 1.2rem;
    opacity: 0.8;
    transition: opacity 0.3s;
}

.social-icon:hover {
    opacity: 1;
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background: #3498db;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    opacity: 0;
    transition: opacity 0.3s;
}

.back-to-top.visible {
    opacity: 1;
}

/* Plant Info Section */
.plant-info {
    margin: 4rem 0;
}

.plant-parallax {
    background: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), url('https://images.unsplash.com/photo-1563453392212-326f5e854473?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80');
    background-attachment: fixed;
    background-size: cover;
    background-position: center;
    color: white;
    padding: 5rem 0;
}

.plant-parallax .section-header {
    text-align: center;
    color: white;
    margin-bottom: 3rem;
}

.plant-parallax .section-header h2 {
    color: white;
}

.plant-parallax .section-subtitle {
    color: rgba(255, 255, 255, 0.9);
    max-width: 700px;
    margin: 1rem auto 0;
}

.plant-features {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    margin: 3rem 0;
}

.plant-feature {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 2rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    backdrop-filter: blur(5px);
}

.plant-feature:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.feature-icon {
    font-size: 2.5rem;
    color: var(--accent-color);
    margin-bottom: 1.5rem;
    background: rgba(255, 255, 255, 0.2);
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.plant-feature h3 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: white;
}

.plant-feature p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.95rem;
    line-height: 1.6;
}

.plant-stats {
    display: flex;
    justify-content: space-between;
    margin: 3rem 0;
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: 8px;
    backdrop-filter: blur(5px);
}

.plant-parallax .stat-item {
    text-align: center;
    padding: 1rem;
}

.plant-parallax .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: 0.5rem;
}

.plant-parallax .stat-label {
    font-size: 1rem;
    color: white;
    font-weight: 500;
}

.plant-parallax .btn-primary {
    background-color: var(--accent-color);
    color: white;
    border: none;
}

.plant-parallax .btn-primary:hover {
    background-color: #0077cc;
    transform: translateY(-3px);
}

/* Our Plant Page Styles */
.plant-overview, .plant-technology, .plant-compliance {
    padding: 4rem 0;
}

.plant-technology {
    background-color: #f9fbfd;
}

.plant-row {
    display: flex;
    gap: 3rem;
    margin-bottom: 3rem;
    align-items: flex-start;
}

.plant-row.reverse {
    flex-direction: row-reverse;
}

.plant-content {
    flex: 1;
    width: 100%;
    text-align: left;
}

.plant-image {
    flex: 1;
    width: 100%;
}

.plant-image img {
    width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.image-caption {
    text-align: center;
    font-style: italic;
    color: #666;
    margin-top: 1rem;
    font-size: 0.9rem;
}

.plant-stats {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    margin: 2rem 0;
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.plant-stats .stat-item {
    text-align: center;
    flex: 1;
    min-width: 120px;
    padding: 1rem;
}

.plant-stats .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--accent-color);
    margin-bottom: 0.5rem;
}

.plant-stats .stat-label {
    font-size: 1rem;
    color: #555;
    font-weight: 600;
}

.section-subheader {
    margin-bottom: 2rem;
}

.section-subheader h3 {
    font-size: 1.8rem;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.section-subheader p {
    color: #666;
    font-size: 1.1rem;
    max-width: 800px;
}

.features-grid {
    margin: 2rem 0;
}

.plant-features-list {
    list-style: none;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1.5rem;
}

.plant-features-list li {
    display: flex;
    align-items: flex-start;
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
}

.plant-features-list li:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0,0,0,0.1);
}

.plant-features-list i {
    color: var(--accent-color);
    font-size: 1.2rem;
    margin-right: 1rem;
    margin-top: 0.2rem;
    min-width: 20px;
}

.feature-content {
    flex: 1;
}

.feature-content strong {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
    font-size: 1.1rem;
}

.feature-content p {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.5;
}

@media (max-width: 768px) {
    .plant-features-list {
        grid-template-columns: 1fr;
    }
}

.tech-highlights {
    margin: 2rem 0;
}

.tech-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 2rem;
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    transition: transform 0.3s ease;
}

.tech-item:hover {
    transform: translateY(-5px);
}

.tech-icon {
    font-size: 2rem;
    color: var(--accent-color);
    margin-right: 1.5rem;
    min-width: 40px;
    text-align: center;
}

.tech-info h3 {
    margin-top: 0;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.tech-certifications {
    margin: 3rem 0;
    text-align: center;
}

.cert-badges {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 2rem;
    margin-top: 1.5rem;
}

.cert-badge {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 200px;
}

.cert-badge i {
    font-size: 2.5rem;
    color: var(--accent-color);
    margin-bottom: 1rem;
}

.compliance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin: 2rem 0;
}

.compliance-item {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    text-align: center;
    position: relative;
    transition: transform 0.3s ease;
}

.compliance-item:hover {
    transform: translateY(-10px);
}

.compliance-icon {
    font-size: 2.5rem;
    color: var(--accent-color);
    margin-bottom: 1rem;
}

.compliance-year {
    display: inline-block;
    background: var(--accent-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.9rem;
    margin-top: 1rem;
}

.quality-commitment {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
    margin-top: 3rem;
}

.quality-points {
    list-style: none;
    margin: 1.5rem 0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.quality-points li {
    display: flex;
    align-items: center;
    gap: 0.8rem;
}

.quality-points i {
    color: var(--accent-color);
}

.plant-cta {
    margin: 4rem 0;
    position: relative;
}

.plant-cta .parallax-image {
    height: auto;
    min-height: 100vh;
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    position: relative;
}

.plant-cta .overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 5rem 0;
}

.plant-cta .image-content {
    max-width: 900px;
    margin: 0 auto;
    padding: 0 2rem;
    text-align: center;
    color: white;
    position: relative;
    z-index: 2;
}

.plant-cta .content-animation {
    opacity: 1;
    transform: translateY(0);
}

.plant-cta h2 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
    color: white;
}

.plant-cta p {
    font-size: 1.1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    color: rgba(255, 255, 255, 0.9);
}

.plant-cta .subtitle {
    display: inline-block;
    background: rgba(255, 255, 255, 0.1);
    padding: 0.5rem 1.5rem;
    border-radius: 50px;
    font-size: 1rem;
    margin-bottom: 1rem;
    color: var(--accent-color);
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.tour-features {
    display: flex;
    justify-content: center;
    gap: 2rem;
    margin: 2rem 0;
    flex-wrap: wrap;
}

.tour-feature {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    background: rgba(255,255,255,0.2);
    padding: 0.8rem 1.5rem;
    border-radius: 50px;
    backdrop-filter: blur(5px);
    transition: transform 0.3s ease, background 0.3s ease;
}

.tour-feature:hover {
    transform: translateY(-5px);
    background: rgba(255,255,255,0.3);
}

.tour-feature i {
    font-size: 1.2rem;
    color: var(--accent-color);
}

.tour-feature span {
    font-weight: 600;
    color: white;
}

.testimonial-quote {
    position: relative;
    padding: 2rem 2rem 2rem 3rem;
    margin: 2rem 0;
    text-align: left;
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    backdrop-filter: blur(5px);
}

.testimonial-quote i {
    position: absolute;
    top: 1.5rem;
    left: 1rem;
    font-size: 1.5rem;
    color: var(--accent-color);
}

.testimonial-quote blockquote {
    margin: 0 0 1rem 0;
    font-style: italic;
    font-size: 1.1rem;
    line-height: 1.6;
    color: white;
}

.testimonial-quote cite {
    font-style: normal;
    font-weight: 600;
    color: rgba(255,255,255,0.8);
    display: block;
}

@media (max-width: 768px) {
    .plant-row, .plant-row.reverse {
        flex-direction: column;
    }

    .plant-stats {
        flex-wrap: wrap;
    }

    .plant-stats .stat-item {
        width: 50%;
        margin-bottom: 1rem;
    }

    .tech-item {
        flex-direction: column;
    }

    .tech-icon {
        margin-right: 0;
        margin-bottom: 1rem;
    }

    .cert-badges {
        flex-direction: column;
        align-items: center;
    }

    .cert-badge {
        width: 100%;
    }

    .tour-features {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .tour-feature {
        width: 100%;
        justify-content: center;
    }

    .plant-cta .parallax-image {
        min-height: 80vh;
    }

    .plant-cta h2 {
        font-size: 2rem;
    }

    .plant-cta .testimonial-quote {
        padding: 1.5rem 1.5rem 1.5rem 2.5rem;
    }

    .plant-cta .testimonial-quote blockquote {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .plant-stats .stat-item {
        width: 100%;
    }

    .plant-features-list {
        grid-template-columns: 1fr;
    }

    .plant-features-list li {
        flex-direction: column;
    }

    .plant-features-list i {
        margin-bottom: 1rem;
        margin-right: 0;
        font-size: 1.5rem;
    }

    .quality-points {
        grid-template-columns: 1fr;
    }

    .plant-cta .parallax-image {
        background-attachment: scroll;
        min-height: auto;
    }

    .plant-cta .overlay {
        position: relative;
        padding: 3rem 0;
    }

    .plant-cta h2 {
        font-size: 1.8rem;
    }

    .plant-cta p {
        font-size: 1rem;
    }

    .plant-cta .subtitle {
        font-size: 0.9rem;
    }

    .plant-cta .cta-buttons {
        flex-direction: column;
        gap: 1rem;
    }

    .plant-cta .btn-secondary {
        margin-left: 0;
    }
}

/* Responsive Adjustments - Updated for 3x3 Layout */
@media (max-width: 1200px) {
    .product-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
    }

    .features {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    /* Mobile Navigation */
    .mobile-menu-btn {
        display: flex !important;
        z-index: 1002;
    }

    .nav-links {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background: white;
        flex-direction: column;
        padding: 2rem;
        transition: left 0.3s ease;
        overflow-y: auto;
        z-index: 1000;
        box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        gap: 0;
        border-top: 1px solid #eee;
    }

    .nav-links.show {
        left: 0 !important;
    }

    .nav-links li {
        margin: 0.5rem 0;
        width: 100%;
        list-style: none;
    }

    .nav-links a {
        display: block;
        padding: 1rem;
        border-radius: 4px;
        transition: background-color 0.3s ease;
        width: 100%;
        text-decoration: none;
        color: #2c3e50;
        font-weight: 500;
    }

    .nav-links a:hover {
        background-color: #f8f9fa;
    }

    /* Mobile dropdown styles */
    .dropdown-menu {
        position: static !important;
        opacity: 1 !important;
        visibility: visible !important;
        transform: none !important;
        box-shadow: none !important;
        background: #f8f9fa;
        width: 100%;
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease;
        padding: 0;
        margin-top: 0.5rem;
        border-radius: 4px;
    }

    .dropdown.active .dropdown-menu {
        max-height: 500px;
        padding: 0.5rem 0;
    }

    .dropdown > a::after {
        content: '\f107';
        font-family: 'Font Awesome 6 Free';
        font-weight: 900;
        float: right;
        margin-top: 0.2rem;
        transition: transform 0.3s ease;
    }

    .dropdown.active > a::after {
        transform: rotate(180deg);
    }

    .dropdown-menu a {
        padding-left: 2rem !important;
        font-size: 0.9rem;
        color: #666;
    }

    .dropdown-menu a:hover {
        padding-left: 2.5rem !important;
        background-color: #e9ecef;
    }

    /* Hero Sections Mobile */
    .products-hero, .about-hero, .plant-hero, .contact-hero {
        height: 300px;
        padding: 0 1rem;
    }

    .products-hero-content h1 {
        font-size: 2rem;
    }

    /* Footer Mobile */
    .footer-nav {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }

    .footer-col iframe {
        max-width: 100%;
        height: 180px;
    }

    /* Home page responsive */
    .intro {
        flex-direction: column;
    }

    .carousel-container {
        height: 80vh;
    }

    .carousel-caption {
        width: 90%;
    }

    .carousel-caption h1 {
        font-size: 2.5rem;
        margin-bottom: 1rem;
    }

    .carousel-caption h2 {
        font-size: 1.5rem;
    }

    .carousel-nav {
        bottom: 30px;
    }

    .carousel-prev,
    .carousel-next {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .dot {
        width: 12px;
        height: 12px;
    }

    .intro-stats {
        flex-direction: column;
    }

    .stat-item {
        margin-bottom: 1rem;
    }

    .plant-features {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .plant-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .plant-parallax .stat-item {
        width: 100%;
        padding: 0.5rem;
    }

    /* Product card responsive */
    .product-image {
        height: 180px;
    }

    .product-card h3 {
        padding: 1.2rem;
        font-size: 1.1rem;
    }
}

@media (max-width: 1024px) {
    .product-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
        padding: 0 1rem;
    }

    .features {
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
        padding: 0 1rem;
    }
}

@media (max-width: 480px) {
    /* Mobile Small Screen Adjustments */
    .product-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 0 1.5rem;
        margin: 2rem 0;
    }

    .product-card {
        max-width: 100%;
        margin: 0 auto;
    }

    .product-image {
        height: 200px;
    }

    .product-content {
        padding: 1rem;
    }

    .product-content h3 {
        font-size: 1.1rem;
    }

    .product-specs {
        flex-direction: column;
        gap: 0.5rem;
        align-items: flex-start;
    }

    /* Why Choose Us Mobile Improvements */
    .why-us {
        padding: 3rem 1rem;
    }

    .features {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 0 1rem;
        margin: 2rem 0;
    }

    .feature {
        padding: 2rem 1.5rem;
        margin-bottom: 1rem;
    }

    /* Product Categories Mobile Improvements */
    .product-categories {
        padding: 3rem 1rem;
    }

    /* Footer Mobile Small */
    .footer-nav {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-col iframe {
        max-width: 100%;
        height: 200px;
    }

    /* Hero Mobile Small */
    .products-hero-content h1 {
        font-size: 1.5rem;
    }

    .products-hero, .about-hero, .plant-hero, .contact-hero {
        height: 250px;
        padding: 0 0.5rem;
    }

    /* CTA Section Mobile */
    .products-cta {
        padding: 2rem 0;
    }

    .cta-content h2 {
        font-size: 1.8rem;
        margin-bottom: 1rem;
    }

    .cta-content p {
        font-size: 1rem;
        margin-bottom: 1.5rem;
    }

    .btn-primary {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
    }

    /* Carousel Mobile Small */
    .carousel-container {
        height: 60vh;
        min-height: 400px;
    }

    .carousel-caption {
        padding: 1rem;
    }

    .carousel-caption h1 {
        font-size: 1.5rem;
        letter-spacing: 1px;
        margin-bottom: 0.5rem;
    }

    .carousel-caption h2 {
        font-size: 1rem;
    }

    .carousel-nav {
        bottom: 15px;
        gap: 8px;
    }

    .carousel-prev,
    .carousel-next {
        width: 30px;
        height: 30px;
        font-size: 0.8rem;
    }

    .dot {
        width: 8px;
        height: 8px;
    }

    /* Typography Mobile Small */
    .intro-text h2 {
        font-size: 1.8rem;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .feature {
        padding: 1.5rem;
    }

    /* Plant section responsive */
    .plant-parallax {
        background-attachment: scroll;
        padding: 3rem 0;
    }

    .plant-features {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .plant-stats {
        flex-direction: column;
        gap: 1rem;
    }

    /* Navigation mobile small */
    .nav-links {
        padding: 1.5rem;
        top: 70px;
        height: calc(100vh - 70px);
    }

    .nav-links a {
        padding: 0.8rem;
        font-size: 0.95rem;
    }

    /* About page mobile improvements */
    .company-info {
        flex-direction: column;
        gap: 2rem;
    }

    .company-text, .company-image {
        width: 100%;
    }

    .company-achievements {
        flex-direction: column;
        gap: 1rem;
        margin-top: 2rem;
    }

    .achievement-item {
        text-align: center;
        padding: 1rem;
    }

    .company-values {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .team-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .member-info h3 {
        font-size: 1.2rem;
    }

    .member-bio {
        font-size: 0.9rem;
        line-height: 1.5;
    }

    /* Contact page mobile improvements */
    .contact-columns {
        flex-direction: column;
        gap: 2rem;
    }

    .contact-column {
        width: 100%;
    }

    .social-media-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }

    .contact-card {
        padding: 1.5rem;
    }

    .map-container iframe {
        height: 300px;
    }

    /* FAQ mobile improvements */
    .faq-question {
        padding: 1rem;
        font-size: 0.95rem;
    }

    .faq-answer-content {
        padding: 1rem;
        font-size: 0.9rem;
    }

    /* Plant page mobile improvements */
    .plant-overview .product-grid {
        grid-template-columns: 1fr !important;
        gap: 2rem;
    }

    /* Products page mobile improvements */
    .download-options {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }

    .download-options .btn-primary,
    .download-options .btn-secondary {
        width: 100%;
        max-width: 280px;
        text-align: center;
    }
}

/* About Page Styles */
.about-hero {
    background: linear-gradient(rgba(0,0,0,0.7), rgba(0,0,0,0.7)), url('https://images.unsplash.com/photo-1532187863486-abf9dbad1b69?ixlib=rb-4.0.3&auto=format&fit=crop&w=1600&q=80');
    background-size: cover;
    background-position: center;
}

.company-info {
    display: flex;
    gap: 3rem;
    margin-bottom: 3rem;
    align-items: flex-start;
}

.company-text {
    flex: 1;
}

.company-image {
    flex: 1;
    position: relative;
}

.company-achievements {
    display: flex;
    justify-content: space-between;
    margin-top: 2rem;
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
}

.achievement-item {
    text-align: center;
    padding: 0 1rem;
}

.achievement-icon {
    font-size: 2rem;
    color: var(--accent-color);
    margin-bottom: 0.5rem;
}

.achievement-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.company-timeline {
    margin: 3rem 0;
    position: relative;
}

.company-timeline::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 20px;
    width: 2px;
    background-color: var(--accent-color);
}

.timeline-item {
    position: relative;
    padding-left: 60px;
    margin-bottom: 2rem;
}

.timeline-year {
    position: absolute;
    left: 0;
    top: 5px;
    width: 40px;
    height: 40px;
    background-color: var(--accent-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 1rem;
    z-index: 1;
    box-shadow: 0 4px 10px rgba(0,160,233,0.3);
}

.timeline-content {
    background-color: #f9f9f9;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.timeline-content h4 {
    margin-top: 0;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.values-row-section {
    background: linear-gradient(to right, #f9fbfd, #edf5fa);
    padding: 5rem 0;
    position: relative;
    overflow: hidden;
}

.values-row-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('https://images.unsplash.com/photo-1587370560942-ad2a04eabb6d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80');
    background-size: cover;
    background-position: center;
    opacity: 0.05;
    z-index: 0;
}

.values-row-section .container {
    position: relative;
    z-index: 1;
}

.company-values {
    display: flex;
    justify-content: space-between;
    gap: 1.5rem;
    margin: 3rem 0;
    flex-wrap: nowrap;
    overflow-x: auto;
    padding-bottom: 1rem;
    -ms-overflow-style: none;  /* Hide scrollbar for IE and Edge */
    scrollbar-width: none;  /* Hide scrollbar for Firefox */
}

.company-values::-webkit-scrollbar {
    display: none; /* Hide scrollbar for Chrome, Safari and Opera */
}

.value-item {
    background: white;
    padding: 2rem 1.5rem;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    flex: 1;
    min-width: 200px;
    max-width: 250px;
}

.value-item:hover {
    transform: translateY(-10px);
    box-shadow: 0 10px 20px rgba(0,0,0,0.1);
}

.value-icon {
    font-size: 2.5rem;
    color: var(--accent-color);
    margin-bottom: 1rem;
    background: rgba(0, 160, 233, 0.1);
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.value-item h3 {
    margin-bottom: 0.8rem;
    color: var(--primary-color);
    font-size: 1.2rem;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
}

.team-member {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
}

.member-image {
    height: 300px;
    position: relative;
    overflow: hidden;
}

.member-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s ease;
}

.member-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(transparent 60%, rgba(0,0,0,0.7));
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    padding-bottom: 2rem;
}

.team-member:hover .member-overlay {
    opacity: 1;
}

.team-member:hover .member-image img {
    transform: scale(1.1);
}

.member-social {
    display: flex;
    gap: 1rem;
}

.member-social a {
    width: 40px;
    height: 40px;
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    transition: all 0.3s ease;
}

.member-social a:hover {
    background: var(--accent-color);
    color: white;
}

.member-info {
    padding: 1.5rem;
}

.member-position {
    color: var(--accent-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

.member-bio {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.6;
}

.about-image-section {
    margin: 4rem 0;
}

.parallax-image {
    height: 500px;
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    position: relative;
}

.overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.6);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.image-content {
    max-width: 800px;
    text-align: center;
    padding: 0 2rem;
}

.content-animation {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 1s forwards 0.5s;
}

.subtitle {
    display: inline-block;
    background: rgba(255,255,255,0.1);
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.9rem;
    margin-bottom: 1rem;
}

.trust-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin: 3rem 0;
    text-align: center;
}

.metric-item {
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
}

.metric-icon {
    font-size: 2.5rem;
    color: var(--accent-color);
    margin-bottom: 1rem;
}

.metric-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
}

.faq-container {
    margin: 3rem 0;
}

.faq-item {
    margin-bottom: 1rem;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.05);
}

.faq-question {
    background: white;
    padding: 1.5rem;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.faq-toggle {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s ease;
}

.faq-answer {
    background: #f9f9f9;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.faq-answer-content {
    padding: 0 1.5rem 1.5rem;
}

.faq-item.active .faq-toggle {
    transform: rotate(45deg);
}

.faq-item.active .faq-answer {
    max-height: 500px;
}

@media (max-width: 768px) {
    .company-info,
    .team-grid {
        flex-direction: column;
    }

    .company-info {
        flex-direction: column;
    }

    .company-achievements {
        flex-wrap: wrap;
    }

    .achievement-item {
        width: 50%;
        margin-bottom: 1.5rem;
    }

    .team-grid {
        grid-template-columns: 1fr;
    }

    .company-values {
        flex-wrap: wrap;
        justify-content: center;
    }

    .value-item {
        min-width: 45%;
        margin-bottom: 1.5rem;
    }
}

@media (max-width: 480px) {
    .achievement-item {
        width: 100%;
    }

    .company-values {
        flex-direction: column;
        align-items: center;
    }

    .value-item {
        min-width: 100%;
        max-width: 100%;
    }

    .trust-metrics {
        grid-template-columns: 1fr;
    }

    .parallax-image {
        background-attachment: scroll;
    }
}

/* Contact Page Styles */
.contact-hero {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: white;
    position: relative;
}

.contact-hero-content {
    max-width: 800px;
    padding: 0 2rem;
    z-index: 1;
}

.hero-animation {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 1s forwards 0.5s;
}

.contact-hero h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.hero-subtitle {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-cta {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
}

.highlight-text {
    color: var(--accent-color);
    position: relative;
    display: inline-block;
}

.highlight-text::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--accent-color);
    transform: scaleX(0);
    transform-origin: left;
    animation: scaleIn 1s forwards 1.2s;
}

.contact-info-section {
    padding: 5rem 0;
    background-color: #f9fbfd;
}

.text-center {
    text-align: center;
}

.section-subtitle {
    max-width: 800px;
    margin: 0 auto 3rem;
    color: #666;
}

.contact-columns {
    display: flex;
    gap: 3rem;
    margin-top: 3rem;
}

.contact-column {
    flex: 1;
}

.column-header {
    margin-bottom: 2rem;
}

.location-card, .contact-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: flex-start;
    transition: transform 0.3s ease;
}

.location-card:hover, .contact-card:hover {
    transform: translateY(-5px);
}

.location-icon, .contact-card-icon {
    font-size: 1.5rem;
    color: var(--accent-color);
    margin-right: 1.5rem;
    min-width: 40px;
    text-align: center;
}

.location-info h4, .contact-card-info h4 {
    margin-top: 0;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.location-detail {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
    color: #666;
}

.text-muted {
    color: #888;
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

.social-media-card {
    background: linear-gradient(135deg, #0056b3, #00a0e9);
    color: white;
}

.social-media-card h4 {
    color: white !important;
}

.social-media-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-top: 1.5rem;
}

.social-media-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: white;
    padding: 0.8rem;
    border-radius: 8px;
    background: rgba(255,255,255,0.1);
    transition: all 0.3s ease;
}

.social-media-item:hover {
    background: rgba(255,255,255,0.2);
    transform: translateY(-3px);
}

.social-media-item i {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.contact-form-section {
    padding: 5rem 0;
    background-color: white;
}

.form-container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    padding: 2.5rem;
    border-radius: 8px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.05);
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: flex;
    gap: 1.5rem;
}

.form-group {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.form-group label {
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.required {
    color: #e74c3c;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.8rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: inherit;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: var(--accent-color);
    outline: none;
}

.checkbox-group {
    flex-direction: row;
    align-items: flex-start;
    gap: 0.8rem;
}

.checkbox-group input {
    margin-top: 0.3rem;
}

.form-submit {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
}

.form-note {
    color: #888;
    font-size: 0.9rem;
}

.form-success {
    text-align: center;
    padding: 2rem;
}

.success-icon {
    font-size: 4rem;
    color: #2ecc71;
    margin-bottom: 1.5rem;
}

.map-section {
    padding: 5rem 0;
    background-color: #f9fbfd;
}

.map-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.map-container {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.faq-section {
    padding: 5rem 0;
    background-color: white;
}

.faq-header {
    text-align: center;
    margin-bottom: 3rem;
}

.faq-wrapper {
    max-width: 900px;
    margin: 0 auto;
}

.faq-question {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.question-icon {
    font-size: 1.2rem;
    color: var(--accent-color);
    min-width: 24px;
}

.faq-question h3 {
    flex: 1;
    margin: 0;
    font-size: 1.1rem;
}

@media (max-width: 768px) {
    .contact-hero h1 {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .contact-columns {
        flex-direction: column;
    }

    .form-row {
        flex-direction: column;
    }

    .hero-cta {
        flex-direction: column;
        align-items: center;
    }

    .social-media-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .contact-hero h1 {
        font-size: 2rem;
    }

    .form-container {
        padding: 1.5rem;
    }

    .form-submit {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .social-media-grid {
        grid-template-columns: 1fr;
    }
}

/* Animation Styles */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

@keyframes scaleIn {
    to {
        transform: scaleX(1);
    }
}

/* Carousel Animation Styles */
.carousel-caption h1 {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 1s forwards 0.5s;
}

.carousel-caption h2 {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 1s forwards 0.8s;
}

.highlight {
    color: var(--accent-color);
    position: relative;
    display: inline-block;
}

.highlight::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--accent-color);
    transform: scaleX(0);
    transform-origin: left;
    animation: scaleIn 1s forwards 1.2s;
}

.carousel-prev,
.carousel-next,
.dot {
    opacity: 0;
    animation: fadeIn 0.5s forwards 1.5s;
}

/* Feature and Product Animations */
.feature, .product-item {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.feature.animated, .product-item.animated {
    opacity: 1;
    transform: translateY(0);
}

/* Additional comprehensive responsive improvements */
@media (max-width: 768px) {
    /* General typography improvements */
    h1 {
        font-size: 2rem !important;
        line-height: 1.2;
    }

    h2 {
        font-size: 1.8rem !important;
        line-height: 1.3;
    }

    h3 {
        font-size: 1.4rem !important;
        line-height: 1.4;
    }

    /* Section padding adjustments */
    section {
        padding: 3rem 0 !important;
    }

    .section-header {
        margin-bottom: 2rem;
    }

    .section-header h2 {
        margin-bottom: 1rem;
    }

    /* Button improvements */
    .btn-primary, .btn-secondary {
        padding: 0.8rem 1.5rem;
        margin: 0.375rem;
        font-size: 0.9rem;
        min-height: 48px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        touch-action: manipulation;
        border-radius: 8px;
        transition: all 0.3s ease;
    }

    /* Download button mobile improvements */
    .download-btn {
        padding: 0.875rem 1.75rem;
        margin: 0.5rem auto;
        min-height: 48px;
        font-size: 0.95rem;
        border-radius: 8px;
        touch-action: manipulation;
        box-shadow: 0 4px 12px rgba(0, 86, 179, 0.2);
    }

    /* Carousel navigation improvements for mobile */
    .carousel-prev, .carousel-next {
        width: 48px;
        height: 48px;
        padding: 0.75rem;
        margin: 0.5rem;
        font-size: 1.2rem;
        border-radius: 50%;
        touch-action: manipulation;
        border-width: 3px;
    }

    .dot {
        width: 18px;
        height: 18px;
        margin: 0.25rem;
        border-width: 3px;
        touch-action: manipulation;
    }

    /* Image responsiveness */
    img {
        max-width: 100%;
        height: auto;
    }

    /* Form improvements */
    input, textarea, select {
        font-size: 16px; /* Prevents zoom on iOS */
        padding: 0.8rem;
        margin: 0.25rem;
        min-height: 48px;
        border-radius: 6px;
        touch-action: manipulation;
    }

    /* Form button improvements */
    button, input[type="submit"], input[type="button"] {
        padding: 0.875rem 1.5rem;
        margin: 0.5rem;
        min-height: 48px;
        font-size: 0.95rem;
        border-radius: 8px;
        touch-action: manipulation;
        transition: all 0.3s ease;
    }

    /* Navigation link improvements for mobile */
    .nav-links a {
        padding: 0.875rem 1rem;
        margin: 0.25rem 0;
        min-height: 48px;
        display: flex;
        align-items: center;
        border-radius: 6px;
        touch-action: manipulation;
    }

    /* Table responsiveness */
    table {
        font-size: 0.9rem;
        overflow-x: auto;
        display: block;
        white-space: nowrap;
    }

    /* Footer improvements */
    .footer-col h3 {
        font-size: 1.1rem;
        margin-bottom: 1rem;
    }

    .footer-col p, .footer-col li {
        font-size: 0.9rem;
        line-height: 1.5;
    }

    /* Back to top button */
    .back-to-top {
        width: 48px;
        height: 48px;
        bottom: 24px;
        right: 24px;
        padding: 0.75rem;
        margin: 0.25rem;
        border-radius: 50%;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        touch-action: manipulation;
    }

    /* Parallax fixes for mobile */
    .plant-parallax,
    .parallax-image {
        background-attachment: scroll !important;
    }
}

/* Extra small devices comprehensive improvements */
@media (max-width: 480px) {
    /* Even smaller typography */
    h1 {
        font-size: 1.8rem !important;
    }

    h2 {
        font-size: 1.5rem !important;
    }

    h3 {
        font-size: 1.2rem !important;
    }

    /* Tighter spacing */
    section {
        padding: 2rem 0 !important;
    }

    /* Smaller buttons */
    .btn-primary, .btn-secondary {
        padding: 0.7rem 1.2rem;
        font-size: 0.85rem;
        margin: 0.375rem;
        min-height: 48px;
        touch-action: manipulation;
    }

    /* Back to top button for small screens */
    .back-to-top {
        width: 52px;
        height: 52px;
        bottom: 20px;
        right: 20px;
        padding: 1rem;
        margin: 0.5rem;
        font-size: 1.1rem;
        box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
    }

    /* Carousel navigation for small screens */
    .carousel-prev, .carousel-next {
        width: 52px;
        height: 52px;
        padding: 1rem;
        margin: 0.75rem;
        font-size: 1.3rem;
        border-width: 4px;
    }

    .dot {
        width: 20px;
        height: 20px;
        margin: 0.375rem;
        border-width: 4px;
    }

    /* Form elements for small screens */
    input, textarea, select {
        padding: 1rem;
        margin: 0.375rem;
        min-height: 52px;
        border-radius: 8px;
    }

    button, input[type="submit"], input[type="button"] {
        padding: 1rem 1.75rem;
        margin: 0.75rem;
        min-height: 52px;
        font-size: 1rem;
        border-radius: 10px;
    }

    /* Download button for small screens */
    .download-btn {
        padding: 1rem 2rem;
        margin: 0.75rem auto;
        min-height: 52px;
        font-size: 1rem;
        border-radius: 10px;
        box-shadow: 0 6px 16px rgba(0, 86, 179, 0.25);
    }

    /* Hero sections */
    .products-hero-content h1,
    .about-hero-content h1,
    .plant-hero-content h1,
    .contact-hero-content h1 {
        font-size: 1.5rem !important;
    }

    /* Carousel adjustments */
    .carousel-container {
        height: 50vh;
        min-height: 350px;
    }

    .carousel-caption h1 {
        font-size: 1.3rem !important;
    }

    .carousel-caption h2 {
        font-size: 0.9rem !important;
    }

    /* Trust metrics and stats */
    .trust-metrics,
    .intro-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .metric-item,
    .stat-item {
        width: 100%;
        text-align: center;
    }

    /* Value items and features */
    .company-values,
    .plant-features {
        grid-template-columns: 1fr !important;
        gap: 1.5rem;
    }
}

/* Download options styling */
.download-options {
    display: flex;
    gap: 1rem;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

/* Text alignment utilities */
.text-center {
    text-align: center;
}

@media (max-width: 768px) {
    .download-options {
        flex-direction: column;
        gap: 1rem;
    }

    .download-options .btn-primary,
    .download-options .btn-secondary {
        width: 100%;
        max-width: 280px;
    }
}
